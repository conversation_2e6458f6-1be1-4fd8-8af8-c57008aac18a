# 物理返回键拦截器 - 简洁版

## 功能特点

- ✅ **默认一直拦截**：无需弹窗确认，直接拦截所有返回操作
- ✅ **兼容iOS和Android**：自动检测设备类型并采用最佳策略
- ✅ **微信环境支持**：完美兼容微信内置浏览器
- ✅ **稳定可靠**：解决了连续点击失效的问题
- ✅ **轻量简洁**：核心代码不到300行，无外部依赖
- ✅ **即插即用**：引入即可使用，无需复杂配置

## 核心改进

### 解决的问题
1. **连续点击失效**：原始代码点击两次返回键后拦截失效
2. **状态管理混乱**：历史记录状态计数不准确
3. **平台兼容性差**：iOS和Android处理方式相同
4. **边界条件处理不当**：异常状态时无法自动恢复

### 解决方案
1. **唯一状态标识**：使用 `backInterceptor` 标识符标记历史记录
2. **防抖处理**：避免快速连续触发导致的状态异常
3. **平台适配**：iOS使用3层历史记录，Android使用8层
4. **自动重建**：检测到异常状态时自动重建拦截器
5. **异步状态维护**：使用 `setTimeout` 确保状态操作时机正确

## 文件说明

### 1. `back2.html` - Vue.js版本
- 基于Vue.js的完整实现
- 包含设备检测和状态管理
- 适合Vue项目集成

### 2. `back-simple.html` - 纯JavaScript版本
- 不依赖任何框架
- 立即执行函数包装
- 适合快速测试和简单项目

### 3. `BackInterceptorSimple.js` - 工具类
- 可复用的JavaScript类
- 支持自定义配置
- 适合复杂项目集成

### 4. `example-usage.html` - 使用示例
- 完整的使用示例
- 状态查看功能
- 演示如何正常退出

## 快速使用

### 方法1：直接使用HTML文件
```html
<!-- 复制 back-simple.html 中的 script 部分到你的页面 -->
<script>
  // 拦截器代码...
</script>
```

### 方法2：使用工具类
```html
<script src="BackInterceptorSimple.js"></script>
<script>
  // 默认配置
  const interceptor = new BackInterceptorSimple()
  
  // 自定义配置
  const interceptor = new BackInterceptorSimple({
    debug: true,  // 启用调试模式
    debounceDelay: 100  // 防抖延迟
  })
</script>
```

### 方法3：Vue.js项目
```html
<!-- 复制 back2.html 中的 Vue 组件代码 -->
<script>
  const app = new Vue({
    // 组件代码...
  })
</script>
```

## 配置选项

| 选项 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `debug` | Boolean | false | 是否显示调试信息 |
| `interceptorKey` | String | 'backInterceptor' | 历史记录状态标识符 |
| `debounceDelay` | Number | 50 | 防抖延迟（毫秒） |

## API方法

| 方法 | 说明 |
|------|------|
| `getStatus()` | 获取当前状态信息 |
| `cleanup()` | 清理拦截器（用于正常退出） |

## 设备兼容性

| 设备类型 | 历史记录深度 | 微信环境深度 |
|----------|--------------|--------------|
| iOS | 3层 | 2层 |
| Android | 8层 | 5层 |
| 其他 | 5层 | 5层 |

## 使用场景

1. **表单页面**：防止用户误操作丢失填写内容
2. **游戏页面**：防止意外退出游戏
3. **重要操作页面**：防止误操作导致流程中断
4. **单页应用**：控制页面导航流程

## 注意事项

1. **正常退出**：需要主动调用 `cleanup()` 方法清理拦截器
2. **调试模式**：生产环境建议关闭 `debug` 选项
3. **性能影响**：拦截器对性能影响极小，可放心使用
4. **浏览器兼容性**：需要支持 History API 的现代浏览器

## 调试信息

启用调试模式后，控制台会显示：
- 拦截器初始化信息
- 设备类型检测结果
- 返回键拦截事件
- 历史状态维护过程
- 拦截器重建过程

## 示例代码

```javascript
// 创建拦截器实例
const interceptor = new BackInterceptorSimple({
  debug: true
})

// 查看状态
console.log(interceptor.getStatus())

// 正常退出时清理
function exitPage() {
  interceptor.cleanup()
  window.location.href = 'home.html'
}
```

## 更新日志

### v1.0.0 (2025-01-29)
- 初始版本发布
- 解决连续点击失效问题
- 支持iOS和Android平台
- 兼容微信内置浏览器
- 提供多种使用方式
