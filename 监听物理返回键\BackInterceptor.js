/**
 * 物理返回键拦截器
 * 兼容 iOS Safari、Android Chrome、微信内置浏览器
 * 默认一直拦截，无弹窗提示
 */
(function() {
  // 设备检测
  const ua = navigator.userAgent
  const isIOS = /iPad|iPhone|iPod/.test(ua) && !window.MSStream
  const isAndroid = /Android/.test(ua)
  const isWechat = /MicroMessenger/.test(ua)
  
  // 状态管理
  let historyDepth = 0
  let lastPopstateTime = 0
  const interceptorKey = 'backInterceptor'
  const debounceDelay = 50
  
  // 获取最佳历史记录深度
  function getOptimalDepth() {
    if (isIOS) {
      return isWechat ? 2 : 3  // iOS 使用较少层级
    } else if (isAndroid) {
      return isWechat ? 5 : 8  // Android 可以使用更多层级
    } else {
      return 5  // 其他设备使用中等层级
    }
  }
  
  // 创建历史记录堆栈
  function createHistoryStack() {
    const depth = getOptimalDepth()
    
    for (let i = 0; i < depth; i++) {
      historyDepth++
      const state = {
        [interceptorKey]: true,
        depth: historyDepth,
        timestamp: Date.now()
      }
      history.pushState(state, '', location.href)
    }
    
    console.log('返回键拦截器已初始化', {
      device: isIOS ? 'iOS' : (isAndroid ? 'Android' : 'Other'),
      isWechat: isWechat,
      depth: historyDepth
    })
  }
  
  // 维护历史状态
  function maintainHistoryState() {
    setTimeout(() => {
      try {
        historyDepth++
        const newState = {
          [interceptorKey]: true,
          depth: historyDepth,
          timestamp: Date.now()
        }
        history.pushState(newState, '', location.href)
      } catch (error) {
        console.error('维护历史状态失败:', error)
        rebuildInterceptor()
      }
    }, 0)
  }
  
  // 重建拦截器
  function rebuildInterceptor() {
    console.log('重建拦截器...')
    historyDepth = 0
    
    const depth = getOptimalDepth()
    for (let i = 0; i < depth; i++) {
      historyDepth++
      const state = {
        [interceptorKey]: true,
        depth: historyDepth,
        timestamp: Date.now()
      }
      history.pushState(state, '', location.href)
    }
  }
  
  // 处理 popstate 事件
  function handlePopstate(event) {
    // 防抖处理
    const now = Date.now()
    if (now - lastPopstateTime < debounceDelay) {
      return false
    }
    lastPopstateTime = now
    
    console.log('物理返回键被拦截', event.state)
    
    // 检查是否是我们的拦截状态
    const isOurState = event.state && event.state[interceptorKey]
    
    // 无论什么情况都拦截并维护历史状态
    maintainHistoryState()
    
    // 如果不是我们的状态，重建拦截器
    if (!isOurState) {
      setTimeout(() => rebuildInterceptor(), 50)
    }
    
    return false
  }
  
  // 初始化拦截器
  function initInterceptor() {
    if (!window.history || !window.history.pushState) {
      console.warn('浏览器不支持 History API')
      return
    }
    
    // 创建历史记录堆栈
    createHistoryStack()
    
    // 添加事件监听
    window.addEventListener('popstate', handlePopstate, false)
    
    // iOS 特殊处理
    if (isIOS) {
      window.addEventListener('pagehide', function(event) {
        console.log('iOS pagehide 事件')
      }, false)
      
      window.addEventListener('hashchange', function(event) {
        console.log('iOS hashchange 事件')
        event.preventDefault()
        maintainHistoryState()
        return false
      }, false)
    }
  }
  
  // 页面加载完成后初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initInterceptor)
  } else {
    initInterceptor()
  }
  
  // 暴露清理方法到全局（可选，用于正常退出）
  window.clearBackInterceptor = function() {
    window.removeEventListener('popstate', handlePopstate)
    console.log('返回键拦截器已清理')
  }
})()
