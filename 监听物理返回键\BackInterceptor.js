/**
 * 物理返回键拦截器
 * 支持 iOS Safari、Android Chrome、微信内置浏览器等环境
 * 
 * <AUTHOR> Assistant
 * @version 2.0.0
 * @date 2025-01-29
 */

class BackInterceptor {
  constructor(options = {}) {
    // 默认配置
    this.config = {
      // 确认消息
      confirmMessage: '确定要离开当前页面吗？',
      // 自定义退出回调
      exitCallback: null,
      // 历史记录深度（null 表示自动检测）
      historyDepth: null,
      // 是否显示控制台日志
      debug: false,
      // 防抖延迟（毫秒）
      debounceDelay: 100,
      // 是否使用自定义确认对话框
      useCustomDialog: false,
      // 自定义对话框配置
      customDialog: {
        title: '确认退出',
        message: '您确定要离开当前页面吗？',
        confirmText: '确定',
        cancelText: '取消'
      },
      ...options
    }
    
    // 状态管理
    this.state = {
      isEnabled: true,
      isInitialized: false,
      interceptCount: 0,
      exitConfirmed: false,
      showingDialog: false,
      lastPopstateTime: 0
    }
    
    // 环境检测
    this.env = this.detectEnvironment()
    
    // 绑定方法上下文
    this.handlePopstate = this.handlePopstate.bind(this)
    this.handleBeforeUnload = this.handleBeforeUnload.bind(this)
    this.handlePageHide = this.handlePageHide.bind(this)
    this.handleHashChange = this.handleHashChange.bind(this)
    
    // 自动初始化
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.init())
    } else {
      this.init()
    }
  }
  
  /**
   * 检测运行环境
   */
  detectEnvironment() {
    const ua = navigator.userAgent
    
    return {
      isIOS: /iPad|iPhone|iPod/.test(ua) && !window.MSStream,
      isAndroid: /Android/.test(ua),
      isWechat: /MicroMessenger/.test(ua),
      isSafari: /Safari/.test(ua) && !/Chrome/.test(ua),
      isChrome: /Chrome/.test(ua),
      userAgent: ua
    }
  }
  
  /**
   * 初始化拦截器
   */
  init() {
    if (!this.isHistoryAPISupported()) {
      this.log('浏览器不支持 History API', 'warn')
      return false
    }
    
    if (this.state.isInitialized) {
      this.log('拦截器已经初始化', 'warn')
      return true
    }
    
    try {
      // 创建历史记录堆栈
      this.createHistoryStack()
      
      // 添加事件监听器
      this.addEventListeners()
      
      // 创建自定义对话框（如果需要）
      if (this.config.useCustomDialog) {
        this.createCustomDialog()
      }
      
      this.state.isInitialized = true
      this.log('拦截器初始化成功', 'info')
      
      return true
      
    } catch (error) {
      this.log('拦截器初始化失败: ' + error.message, 'error')
      return false
    }
  }
  
  /**
   * 检查 History API 支持
   */
  isHistoryAPISupported() {
    return !!(window.history && window.history.pushState)
  }
  
  /**
   * 创建历史记录堆栈
   */
  createHistoryStack() {
    const depth = this.config.historyDepth || this.getOptimalDepth()
    
    for (let i = 0; i < depth; i++) {
      const state = {
        backInterceptor: true,
        depth: i + 1,
        timestamp: Date.now(),
        platform: this.env.isIOS ? 'iOS' : 'Android'
      }
      
      history.pushState(state, '', location.href)
    }
    
    this.log(`创建了 ${depth} 层历史记录堆栈`)
  }
  
  /**
   * 获取最佳历史记录深度
   */
  getOptimalDepth() {
    if (this.env.isIOS) {
      return this.env.isWechat ? 2 : 3
    } else {
      return this.env.isWechat ? 5 : 8
    }
  }
  
  /**
   * 添加事件监听器
   */
  addEventListeners() {
    // 主要的 popstate 事件
    window.addEventListener('popstate', this.handlePopstate, false)
    
    // 页面刷新拦截
    window.addEventListener('beforeunload', this.handleBeforeUnload, false)
    
    // iOS 特殊处理
    if (this.env.isIOS) {
      window.addEventListener('pagehide', this.handlePageHide, false)
      window.addEventListener('hashchange', this.handleHashChange, false)
    }
  }
  
  /**
   * 处理 popstate 事件（核心拦截逻辑）
   */
  handlePopstate(event) {
    if (!this.state.isEnabled || this.state.exitConfirmed) {
      return true
    }
    
    // 防抖处理
    const now = Date.now()
    if (now - this.state.lastPopstateTime < this.config.debounceDelay) {
      return false
    }
    this.state.lastPopstateTime = now
    
    this.log('Popstate 事件触发', 'info', { state: event.state })
    
    // 如果正在显示对话框，维护状态
    if (this.state.showingDialog) {
      this.maintainHistoryState()
      return false
    }
    
    this.state.interceptCount++
    
    // 显示确认对话框
    this.showConfirmation()
    
    // 维护历史状态
    this.maintainHistoryState()
    
    return false
  }
  
  /**
   * 显示确认对话框
   */
  showConfirmation() {
    if (this.config.useCustomDialog && this.customDialog) {
      this.showCustomDialog()
    } else {
      this.showNativeDialog()
    }
  }
  
  /**
   * 显示原生确认对话框
   */
  showNativeDialog() {
    // 使用 setTimeout 确保在事件循环后执行
    setTimeout(() => {
      const confirmed = confirm(this.config.confirmMessage)
      this.handleConfirmationResult(confirmed)
    }, 0)
  }
  
  /**
   * 显示自定义确认对话框
   */
  showCustomDialog() {
    if (!this.customDialog) return
    
    this.state.showingDialog = true
    this.customDialog.style.display = 'block'
    
    // 防止页面滚动
    document.body.style.overflow = 'hidden'
  }
  
  /**
   * 创建自定义对话框
   */
  createCustomDialog() {
    const dialog = document.createElement('div')
    dialog.className = 'back-interceptor-dialog'
    dialog.innerHTML = `
      <div class="back-interceptor-overlay"></div>
      <div class="back-interceptor-content">
        <h3>${this.config.customDialog.title}</h3>
        <p>${this.config.customDialog.message}</p>
        <div class="back-interceptor-buttons">
          <button class="back-interceptor-confirm">${this.config.customDialog.confirmText}</button>
          <button class="back-interceptor-cancel">${this.config.customDialog.cancelText}</button>
        </div>
      </div>
    `
    
    // 添加样式
    this.addDialogStyles()
    
    // 添加事件监听
    const confirmBtn = dialog.querySelector('.back-interceptor-confirm')
    const cancelBtn = dialog.querySelector('.back-interceptor-cancel')
    const overlay = dialog.querySelector('.back-interceptor-overlay')
    
    confirmBtn.addEventListener('click', () => this.handleConfirmationResult(true))
    cancelBtn.addEventListener('click', () => this.handleConfirmationResult(false))
    overlay.addEventListener('click', () => this.handleConfirmationResult(false))
    
    document.body.appendChild(dialog)
    this.customDialog = dialog
  }
  
  /**
   * 添加对话框样式
   */
  addDialogStyles() {
    if (document.getElementById('back-interceptor-styles')) return
    
    const style = document.createElement('style')
    style.id = 'back-interceptor-styles'
    style.textContent = `
      .back-interceptor-dialog {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 9999;
        display: none;
      }
      .back-interceptor-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
      }
      .back-interceptor-content {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        min-width: 280px;
        text-align: center;
      }
      .back-interceptor-content h3 {
        margin: 0 0 15px 0;
        color: #333;
      }
      .back-interceptor-content p {
        margin: 0 0 20px 0;
        color: #666;
        line-height: 1.5;
      }
      .back-interceptor-buttons button {
        margin: 0 5px;
        padding: 10px 20px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        min-width: 80px;
      }
      .back-interceptor-confirm {
        background-color: #f44336;
        color: white;
      }
      .back-interceptor-cancel {
        background-color: #9e9e9e;
        color: white;
      }
    `
    
    document.head.appendChild(style)
  }
  
  /**
   * 处理确认结果
   */
  handleConfirmationResult(confirmed) {
    this.hideCustomDialog()
    
    if (confirmed) {
      this.state.exitConfirmed = true
      
      if (this.config.exitCallback) {
        this.config.exitCallback()
      } else {
        // 默认行为：返回上一页
        this.performExit()
      }
    } else {
      // 用户取消，维护历史状态
      this.maintainHistoryState()
    }
  }
  
  /**
   * 隐藏自定义对话框
   */
  hideCustomDialog() {
    if (this.customDialog) {
      this.customDialog.style.display = 'none'
      this.state.showingDialog = false
      document.body.style.overflow = ''
    }
  }
  
  /**
   * 执行退出操作
   */
  performExit() {
    const depth = this.config.historyDepth || this.getOptimalDepth()
    window.history.go(-(depth + 1))
  }
  
  /**
   * 维护历史状态
   */
  maintainHistoryState() {
    setTimeout(() => {
      try {
        const state = {
          backInterceptor: true,
          depth: this.state.interceptCount,
          timestamp: Date.now(),
          platform: this.env.isIOS ? 'iOS' : 'Android'
        }
        
        history.pushState(state, '', location.href)
        this.log('历史状态已维护')
        
      } catch (error) {
        this.log('维护历史状态失败: ' + error.message, 'error')
      }
    }, 0)
  }
  
  /**
   * 处理页面刷新拦截
   */
  handleBeforeUnload(event) {
    if (!this.state.exitConfirmed && this.state.isEnabled) {
      const message = '确定要离开此页面吗？'
      event.preventDefault()
      event.returnValue = message
      return message
    }
  }
  
  /**
   * iOS 特殊事件处理
   */
  handlePageHide(event) {
    if (this.env.isIOS && !this.state.exitConfirmed && this.state.isEnabled) {
      this.log('iOS pagehide 事件')
    }
  }
  
  handleHashChange(event) {
    if (this.env.isIOS && !this.state.exitConfirmed && this.state.isEnabled) {
      this.log('iOS hashchange 事件')
      event.preventDefault()
      this.showConfirmation()
      return false
    }
  }
  
  /**
   * 启用拦截器
   */
  enable() {
    this.state.isEnabled = true
    this.log('拦截器已启用')
  }
  
  /**
   * 禁用拦截器
   */
  disable() {
    this.state.isEnabled = false
    this.log('拦截器已禁用')
  }
  
  /**
   * 重置拦截器
   */
  reset() {
    this.state.interceptCount = 0
    this.state.exitConfirmed = false
    this.state.showingDialog = false
    this.hideCustomDialog()
    this.log('拦截器已重置')
  }
  
  /**
   * 销毁拦截器
   */
  destroy() {
    // 移除事件监听器
    window.removeEventListener('popstate', this.handlePopstate)
    window.removeEventListener('beforeunload', this.handleBeforeUnload)
    window.removeEventListener('pagehide', this.handlePageHide)
    window.removeEventListener('hashchange', this.handleHashChange)
    
    // 移除自定义对话框
    if (this.customDialog) {
      this.customDialog.remove()
      this.customDialog = null
    }
    
    // 重置状态
    this.state.isInitialized = false
    this.log('拦截器已销毁')
  }
  
  /**
   * 获取当前状态
   */
  getStatus() {
    return {
      ...this.state,
      environment: this.env,
      config: this.config
    }
  }
  
  /**
   * 日志输出
   */
  log(message, level = 'log', data = null) {
    if (!this.config.debug) return
    
    const prefix = '[BackInterceptor]'
    const methods = {
      log: console.log,
      info: console.info,
      warn: console.warn,
      error: console.error
    }
    
    const method = methods[level] || console.log
    
    if (data) {
      method(prefix, message, data)
    } else {
      method(prefix, message)
    }
  }
}

// 导出类（支持多种模块系统）
if (typeof module !== 'undefined' && module.exports) {
  module.exports = BackInterceptor
} else if (typeof define === 'function' && define.amd) {
  define([], function() { return BackInterceptor })
} else {
  window.BackInterceptor = BackInterceptor
}
