/**
 * 简洁版物理返回键拦截器
 * 兼容 iOS Safari、Android Chrome、微信内置浏览器
 * 默认一直拦截，无弹窗提示
 * 
 * @version 1.0.0
 * @date 2025-01-29
 */

class BackInterceptorSimple {
  constructor(options = {}) {
    // 配置选项
    this.config = {
      debug: false,
      interceptorKey: 'backInterceptor',
      debounceDelay: 50,
      ...options
    }
    
    // 设备检测
    this.env = this.detectEnvironment()
    
    // 状态管理
    this.state = {
      historyDepth: 0,
      lastPopstateTime: 0,
      isInitialized: false
    }
    
    // 绑定方法上下文
    this.handlePopstate = this.handlePopstate.bind(this)
    this.handlePageHide = this.handlePageHide.bind(this)
    this.handleHashChange = this.handleHashChange.bind(this)
    
    // 自动初始化
    this.init()
  }
  
  /**
   * 检测运行环境
   */
  detectEnvironment() {
    const ua = navigator.userAgent
    
    return {
      isIOS: /iPad|iPhone|iPod/.test(ua) && !window.MSStream,
      isAndroid: /Android/.test(ua),
      isWechat: /MicroMessenger/.test(ua),
      userAgent: ua
    }
  }
  
  /**
   * 初始化拦截器
   */
  init() {
    if (!this.isHistoryAPISupported()) {
      this.log('浏览器不支持 History API', 'warn')
      return false
    }
    
    if (this.state.isInitialized) {
      this.log('拦截器已经初始化', 'warn')
      return true
    }
    
    try {
      // 等待DOM加载完成
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => this.setup())
      } else {
        this.setup()
      }
      
      return true
    } catch (error) {
      this.log('拦截器初始化失败: ' + error.message, 'error')
      return false
    }
  }
  
  /**
   * 设置拦截器
   */
  setup() {
    // 创建历史记录堆栈
    this.createHistoryStack()
    
    // 添加事件监听器
    this.addEventListeners()
    
    this.state.isInitialized = true
    
    this.log('返回键拦截器已初始化', 'info', {
      device: this.getDeviceType(),
      depth: this.state.historyDepth
    })
  }
  
  /**
   * 检查 History API 支持
   */
  isHistoryAPISupported() {
    return !!(window.history && window.history.pushState)
  }
  
  /**
   * 获取设备类型字符串
   */
  getDeviceType() {
    if (this.env.isIOS) return 'iOS'
    if (this.env.isAndroid) return 'Android'
    return 'Other'
  }
  
  /**
   * 获取最佳历史记录深度
   */
  getOptimalDepth() {
    if (this.env.isIOS) {
      // iOS 使用较少的层级避免内存问题
      return this.env.isWechat ? 2 : 3
    } else if (this.env.isAndroid) {
      // Android 可以使用更多层级
      return this.env.isWechat ? 5 : 8
    } else {
      // 其他设备使用中等层级
      return 5
    }
  }
  
  /**
   * 创建历史记录堆栈
   */
  createHistoryStack() {
    const depth = this.getOptimalDepth()
    
    for (let i = 0; i < depth; i++) {
      this.state.historyDepth++
      const state = {
        [this.config.interceptorKey]: true,
        depth: this.state.historyDepth,
        timestamp: Date.now(),
        platform: this.getDeviceType()
      }
      history.pushState(state, '', location.href)
    }
    
    this.log(`创建了 ${depth} 层历史记录堆栈`)
  }
  
  /**
   * 添加事件监听器
   */
  addEventListeners() {
    // 主要的 popstate 事件
    window.addEventListener('popstate', this.handlePopstate, false)
    
    // iOS 特殊处理
    if (this.env.isIOS) {
      window.addEventListener('pagehide', this.handlePageHide, false)
      window.addEventListener('hashchange', this.handleHashChange, false)
    }
    
    // 页面卸载时清理
    window.addEventListener('beforeunload', () => this.cleanup(), false)
  }
  
  /**
   * 处理 popstate 事件（核心拦截逻辑）
   */
  handlePopstate(event) {
    // 防抖处理
    const now = Date.now()
    if (now - this.state.lastPopstateTime < this.config.debounceDelay) {
      return false
    }
    this.state.lastPopstateTime = now
    
    this.log('物理返回键被拦截', 'info', {
      state: event.state,
      historyDepth: this.state.historyDepth
    })
    
    // 检查是否是我们的拦截状态
    const isOurState = event.state && event.state[this.config.interceptorKey]
    
    // 无论什么情况都拦截并维护历史状态
    this.maintainHistoryState()
    
    // 如果不是我们的状态，重建拦截器
    if (!isOurState) {
      setTimeout(() => this.rebuildInterceptor(), 50)
    }
    
    return false
  }
  
  /**
   * 维护历史状态
   */
  maintainHistoryState() {
    setTimeout(() => {
      try {
        this.state.historyDepth++
        const newState = {
          [this.config.interceptorKey]: true,
          depth: this.state.historyDepth,
          timestamp: Date.now(),
          platform: this.getDeviceType()
        }
        
        history.pushState(newState, '', location.href)
        this.log('历史状态已维护', 'debug', { depth: this.state.historyDepth })
        
      } catch (error) {
        this.log('维护历史状态失败: ' + error.message, 'error')
        this.rebuildInterceptor()
      }
    }, 0)
  }
  
  /**
   * 重建拦截器
   */
  rebuildInterceptor() {
    this.log('重建拦截器...')
    
    this.state.historyDepth = 0
    const depth = this.getOptimalDepth()
    
    for (let i = 0; i < depth; i++) {
      this.state.historyDepth++
      const state = {
        [this.config.interceptorKey]: true,
        depth: this.state.historyDepth,
        timestamp: Date.now(),
        platform: this.getDeviceType()
      }
      history.pushState(state, '', location.href)
    }
    
    this.log('拦截器重建完成', 'info', { depth: this.state.historyDepth })
  }
  
  /**
   * iOS 特殊事件处理
   */
  handlePageHide(event) {
    this.log('iOS pagehide 事件', 'debug')
  }
  
  handleHashChange(event) {
    this.log('iOS hashchange 事件', 'debug')
    event.preventDefault()
    this.maintainHistoryState()
    return false
  }
  
  /**
   * 获取当前状态
   */
  getStatus() {
    return {
      ...this.state,
      environment: this.env,
      config: this.config
    }
  }
  
  /**
   * 清理资源
   */
  cleanup() {
    window.removeEventListener('popstate', this.handlePopstate)
    window.removeEventListener('pagehide', this.handlePageHide)
    window.removeEventListener('hashchange', this.handleHashChange)
    
    this.state.isInitialized = false
    this.log('拦截器已清理')
  }
  
  /**
   * 日志输出
   */
  log(message, level = 'log', data = null) {
    if (!this.config.debug) return
    
    const prefix = '[BackInterceptor]'
    const methods = {
      log: console.log,
      info: console.info,
      warn: console.warn,
      error: console.error,
      debug: console.debug
    }
    
    const method = methods[level] || console.log
    
    if (data) {
      method(prefix, message, data)
    } else {
      method(prefix, message)
    }
  }
}

// 导出类（支持多种模块系统）
if (typeof module !== 'undefined' && module.exports) {
  module.exports = BackInterceptorSimple
} else if (typeof define === 'function' && define.amd) {
  define([], function() { return BackInterceptorSimple })
} else {
  window.BackInterceptorSimple = BackInterceptorSimple
}
