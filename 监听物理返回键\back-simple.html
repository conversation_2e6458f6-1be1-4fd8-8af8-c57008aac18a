<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>物理返回键拦截 - 简洁版</title>
    <meta name="description" content="简洁的物理返回键拦截功能" />
    <meta
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"
      name="viewport"
    />
  </head>

  <body>
    <div>页面2 - 返回键已拦截</div>

    <script>
      // 物理返回键拦截器 - 简洁版
      (function() {
        // 配置参数
        const config = {
          interceptorKey: 'backInterceptor',
          debug: true
        }
        
        // 设备检测
        const ua = navigator.userAgent
        const isIOS = /iPad|iPhone|iPod/.test(ua) && !window.MSStream
        const isAndroid = /Android/.test(ua)
        const isWechat = /MicroMessenger/.test(ua)
        
        // 状态管理
        let historyDepth = 0
        let lastPopstateTime = 0
        const debounceDelay = 50
        
        // 获取最佳历史记录深度
        function getOptimalDepth() {
          if (isIOS) {
            return isWechat ? 2 : 3  // iOS 使用较少层级
          } else if (isAndroid) {
            return isWechat ? 5 : 8  // Android 可以使用更多层级
          } else {
            return 5  // 其他设备使用中等层级
          }
        }
        
        // 创建历史记录堆栈
        function createHistoryStack() {
          const depth = getOptimalDepth()
          
          for (let i = 0; i < depth; i++) {
            historyDepth++
            const state = {
              [config.interceptorKey]: true,
              depth: historyDepth,
              timestamp: Date.now(),
              platform: isIOS ? 'iOS' : (isAndroid ? 'Android' : 'Other')
            }
            history.pushState(state, '', location.href)
          }
          
          if (config.debug) {
            console.log('返回键拦截器已初始化', {
              device: isIOS ? 'iOS' : (isAndroid ? 'Android' : 'Other'),
              isWechat: isWechat,
              depth: historyDepth
            })
          }
        }
        
        // 维护历史状态
        function maintainHistoryState() {
          setTimeout(() => {
            try {
              historyDepth++
              const newState = {
                [config.interceptorKey]: true,
                depth: historyDepth,
                timestamp: Date.now(),
                platform: isIOS ? 'iOS' : (isAndroid ? 'Android' : 'Other')
              }
              history.pushState(newState, '', location.href)
              
              if (config.debug) {
                console.log('历史状态已维护', { depth: historyDepth })
              }
            } catch (error) {
              console.error('维护历史状态失败:', error)
              rebuildInterceptor()
            }
          }, 0)
        }
        
        // 重建拦截器
        function rebuildInterceptor() {
          if (config.debug) {
            console.log('重建拦截器...')
          }
          
          historyDepth = 0
          const depth = getOptimalDepth()
          
          for (let i = 0; i < depth; i++) {
            historyDepth++
            const state = {
              [config.interceptorKey]: true,
              depth: historyDepth,
              timestamp: Date.now(),
              platform: isIOS ? 'iOS' : (isAndroid ? 'Android' : 'Other')
            }
            history.pushState(state, '', location.href)
          }
          
          if (config.debug) {
            console.log('拦截器重建完成', { depth: historyDepth })
          }
        }
        
        // 处理 popstate 事件
        function handlePopstate(event) {
          // 防抖处理
          const now = Date.now()
          if (now - lastPopstateTime < debounceDelay) {
            return false
          }
          lastPopstateTime = now
          
          if (config.debug) {
            console.log('物理返回键被拦截', {
              state: event.state,
              historyDepth: historyDepth
            })
          }
          
          // 检查是否是我们的拦截状态
          const isOurState = event.state && event.state[config.interceptorKey]
          
          // 无论什么情况都拦截并维护历史状态
          maintainHistoryState()
          
          // 如果不是我们的状态，重建拦截器
          if (!isOurState) {
            setTimeout(() => rebuildInterceptor(), 50)
          }
          
          return false
        }
        
        // 初始化拦截器
        function initInterceptor() {
          if (!window.history || !window.history.pushState) {
            console.warn('浏览器不支持 History API')
            return false
          }
          
          try {
            // 创建历史记录堆栈
            createHistoryStack()
            
            // 添加事件监听
            window.addEventListener('popstate', handlePopstate, false)
            
            // iOS 特殊处理
            if (isIOS) {
              window.addEventListener('pagehide', function(event) {
                if (config.debug) {
                  console.log('iOS pagehide 事件')
                }
              }, false)
              
              // 某些 iOS 版本需要 hashchange 监听
              window.addEventListener('hashchange', function(event) {
                if (config.debug) {
                  console.log('iOS hashchange 事件')
                }
                event.preventDefault()
                maintainHistoryState()
                return false
              }, false)
            }
            
            return true
          } catch (error) {
            console.error('拦截器初始化失败:', error)
            return false
          }
        }
        
        // 页面加载完成后初始化
        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', initInterceptor)
        } else {
          initInterceptor()
        }
        
        // 页面卸载时清理
        window.addEventListener('beforeunload', function() {
          window.removeEventListener('popstate', handlePopstate)
        })
        
        // 暴露到全局（可选，用于调试）
        if (config.debug) {
          window.backInterceptor = {
            getStatus: function() {
              return {
                historyDepth: historyDepth,
                isIOS: isIOS,
                isAndroid: isAndroid,
                isWechat: isWechat
              }
            },
            rebuild: rebuildInterceptor
          }
        }
      })()
    </script>
  </body>
</html>
