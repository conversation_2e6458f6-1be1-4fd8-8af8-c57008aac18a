<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>vue测试</title>
    <meta name="description" content="aipha" />
    <meta
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"
      name="viewport"
    />
    <script src="../js/vue.min.js"></script>
    <!-- 引入物理返回键拦截器 - 修复版 -->
    <script src="backInterceptor-fixed.js"></script>
    <style></style>
  </head>

  <body>
    <div id="app">页面2</div>

    <script>
      // Vue 实例（拦截逻辑已移至 backInterceptor.js）
      const app = new Vue({
        el: "#app",
        data: {},
        methods: {}
      })
    </script>
  </body>
</html>
